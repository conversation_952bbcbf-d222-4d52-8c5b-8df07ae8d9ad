"""
Piper TTS integration module for generating speech from text using ONNX models.
"""

import json
import io
import wave
from pathlib import Path
from typing import Optional, Union, Dict, Any
import numpy as np
import onnxruntime
from piper_phonemize import phonemize_espeak


class PiperTTS:
    """Piper Text-to-Speech engine using ONNX models."""
    
    def __init__(self, model_path: Union[str, Path], config_path: Union[str, Path]):
        """
        Initialize the Piper TTS engine.
        
        Args:
            model_path: Path to the ONNX model file
            config_path: Path to the JSON config file
        """
        self.model_path = Path(model_path)
        self.config_path = Path(config_path)
        
        # Load configuration
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # Initialize ONNX runtime session
        self.session = onnxruntime.InferenceSession(
            str(self.model_path),
            providers=['CPUExecutionProvider']
        )
        
        # Extract model information
        self.sample_rate = self.config['audio']['sample_rate']
        self.num_speakers = self.config.get('num_speakers', 1)
        self.speaker_id_map = self.config.get('speaker_id_map', {})
        self.phoneme_id_map = self.config['phoneme_id_map']
        self.language_code = self.config['espeak']['voice']
        
        # Default inference parameters
        self.default_noise_scale = self.config['inference'].get('noise_scale', 0.667)
        self.default_length_scale = self.config['inference'].get('length_scale', 1.0)
        self.default_noise_w = self.config['inference'].get('noise_w', 0.8)
    
    def get_available_speakers(self) -> Dict[str, int]:
        """Get available speaker IDs and their names."""
        return self.speaker_id_map.copy()
    
    def text_to_phonemes(self, text: str) -> str:
        """Convert text to phonemes using espeak."""
        phonemes = phonemize_espeak(text, self.language_code)
        return phonemes[0] if phonemes else ""
    
    def phonemes_to_ids(self, phonemes: str) -> np.ndarray:
        """Convert phonemes to phoneme IDs."""
        phoneme_ids = []
        for phoneme in phonemes:
            if phoneme in self.phoneme_id_map:
                phoneme_ids.extend(self.phoneme_id_map[phoneme])
            else:
                # Use unknown phoneme ID if available, otherwise skip
                if '_' in self.phoneme_id_map:
                    phoneme_ids.extend(self.phoneme_id_map['_'])
        
        return np.array(phoneme_ids, dtype=np.int64)
    
    def synthesize(
        self,
        text: str,
        speaker_id: Optional[int] = None,
        noise_scale: Optional[float] = None,
        length_scale: Optional[float] = None,
        noise_w: Optional[float] = None
    ) -> np.ndarray:
        """
        Synthesize speech from text.
        
        Args:
            text: Input text to synthesize
            speaker_id: Speaker ID (0 to num_speakers-1)
            noise_scale: Noise scale for variability (default: 0.667)
            length_scale: Length scale for speech rate (default: 1.0, <1.0 = faster, >1.0 = slower)
            noise_w: Noise width parameter (default: 0.8)
            
        Returns:
            Audio data as numpy array
        """
        # Set default parameters
        if noise_scale is None:
            noise_scale = self.default_noise_scale
        if length_scale is None:
            length_scale = self.default_length_scale
        if noise_w is None:
            noise_w = self.default_noise_w
        if speaker_id is None:
            speaker_id = 0
        
        # Validate speaker ID
        if speaker_id < 0 or speaker_id >= self.num_speakers:
            raise ValueError(f"Speaker ID must be between 0 and {self.num_speakers - 1}")
        
        # Convert text to phonemes
        phonemes = self.text_to_phonemes(text)
        if not phonemes:
            raise ValueError("Could not convert text to phonemes")
        
        # Convert phonemes to IDs
        phoneme_ids = self.phonemes_to_ids(phonemes)
        if len(phoneme_ids) == 0:
            raise ValueError("Could not convert phonemes to IDs")
        
        # Prepare inputs for ONNX model
        inputs = {
            'input': phoneme_ids.reshape(1, -1),
            'input_lengths': np.array([len(phoneme_ids)], dtype=np.int64),
            'scales': np.array([noise_scale, length_scale, noise_w], dtype=np.float32)
        }
        
        # Add speaker ID if multi-speaker model
        if self.num_speakers > 1:
            inputs['sid'] = np.array([speaker_id], dtype=np.int64)
        
        # Run inference
        outputs = self.session.run(None, inputs)
        audio = outputs[0].squeeze()
        
        return audio
    
    def synthesize_to_wav(
        self,
        text: str,
        speaker_id: Optional[int] = None,
        noise_scale: Optional[float] = None,
        length_scale: Optional[float] = None,
        noise_w: Optional[float] = None
    ) -> bytes:
        """
        Synthesize speech and return as WAV bytes.
        
        Args:
            text: Input text to synthesize
            speaker_id: Speaker ID (0 to num_speakers-1)
            noise_scale: Noise scale for variability
            length_scale: Length scale for speech rate
            noise_w: Noise width parameter
            
        Returns:
            WAV audio data as bytes
        """
        # Generate audio
        audio = self.synthesize(text, speaker_id, noise_scale, length_scale, noise_w)
        
        # Convert to 16-bit PCM
        audio_int16 = (audio * 32767).astype(np.int16)
        
        # Create WAV file in memory
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        return wav_buffer.getvalue()
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            'language': self.config.get('language', {}),
            'sample_rate': self.sample_rate,
            'num_speakers': self.num_speakers,
            'available_speakers': list(self.speaker_id_map.keys()),
            'piper_version': self.config.get('piper_version', 'unknown'),
            'dataset': self.config.get('dataset', 'unknown')
        }
