"""
FastAPI application for Piper TTS service.
"""

from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.responses import Response
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import logging
from pathlib import Path

from .piper_tts import PiperTTS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Piper TTS API",
    description="Text-to-Speech API using Piper ONNX models with speaker and speech rate control",
    version="1.0.0"
)

# Global TTS engine instance
tts_engine: Optional[PiperTTS] = None

# Pydantic models for request/response
class TTSRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize", min_length=1, max_length=1000)
    speaker_id: Optional[int] = Field(None, description="Speaker ID (0 to num_speakers-1)", ge=0)
    speech_rate: Optional[float] = Field(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0)
    variability: Optional[float] = Field(0.667, description="Voice variability (0.0 = monotone, 1.0 = very variable)", ge=0.0, le=1.0)
    noise_w: Optional[float] = Field(0.8, description="Noise width parameter", ge=0.0, le=1.0)

class ModelInfo(BaseModel):
    language: Dict[str, Any]
    sample_rate: int
    num_speakers: int
    available_speakers: list
    piper_version: str
    dataset: str

class SpeakerInfo(BaseModel):
    speaker_id: int
    speaker_name: str

@app.on_event("startup")
async def startup_event():
    """Initialize the TTS engine on startup."""
    global tts_engine
    
    try:
        # Model paths
        model_path = Path("models/ne_NP-google-medium.onnx")
        config_path = Path("models/ne_NP-google-medium.onnx.json")
        
        if not model_path.exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        # Initialize TTS engine
        tts_engine = PiperTTS(model_path, config_path)
        logger.info(f"TTS engine initialized successfully")
        logger.info(f"Model info: {tts_engine.get_model_info()}")
        
    except Exception as e:
        logger.error(f"Failed to initialize TTS engine: {e}")
        raise

@app.get("/", summary="Health check")
async def root():
    """Health check endpoint."""
    return {"message": "Piper TTS API is running", "status": "healthy"}

@app.get("/model/info", response_model=ModelInfo, summary="Get model information")
async def get_model_info():
    """Get information about the loaded TTS model."""
    if tts_engine is None:
        raise HTTPException(status_code=500, detail="TTS engine not initialized")
    
    return tts_engine.get_model_info()

@app.get("/speakers", summary="Get available speakers")
async def get_speakers():
    """Get list of available speakers."""
    if tts_engine is None:
        raise HTTPException(status_code=500, detail="TTS engine not initialized")
    
    speakers = tts_engine.get_available_speakers()
    return {
        "num_speakers": tts_engine.num_speakers,
        "speakers": [
            {"speaker_id": speaker_id, "speaker_name": name}
            for name, speaker_id in speakers.items()
        ]
    }

@app.post("/synthesize", summary="Synthesize speech from text")
async def synthesize_speech(request: TTSRequest):
    """
    Synthesize speech from text with speaker and speech rate control.
    
    Returns WAV audio data.
    """
    if tts_engine is None:
        raise HTTPException(status_code=500, detail="TTS engine not initialized")
    
    try:
        # Convert speech_rate to length_scale (inverse relationship)
        length_scale = 1.0 / request.speech_rate if request.speech_rate else 1.0
        
        # Generate audio
        wav_data = tts_engine.synthesize_to_wav(
            text=request.text,
            speaker_id=request.speaker_id,
            noise_scale=request.variability,
            length_scale=length_scale,
            noise_w=request.noise_w
        )
        
        # Return WAV file
        return Response(
            content=wav_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=speech.wav",
                "Content-Length": str(len(wav_data))
            }
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error during synthesis: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during synthesis")

@app.get("/synthesize", summary="Synthesize speech from text (GET)")
async def synthesize_speech_get(
    text: str = Query(..., description="Text to synthesize", min_length=1, max_length=1000),
    speaker_id: Optional[int] = Query(None, description="Speaker ID", ge=0),
    speech_rate: Optional[float] = Query(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0),
    variability: Optional[float] = Query(0.667, description="Voice variability", ge=0.0, le=1.0),
    noise_w: Optional[float] = Query(0.8, description="Noise width parameter", ge=0.0, le=1.0)
):
    """
    Synthesize speech from text using GET parameters.
    
    Returns WAV audio data.
    """
    request = TTSRequest(
        text=text,
        speaker_id=speaker_id,
        speech_rate=speech_rate,
        variability=variability,
        noise_w=noise_w
    )
    return await synthesize_speech(request)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
